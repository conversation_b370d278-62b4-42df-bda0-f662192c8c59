# Container Generic Methods Usage Example

This document demonstrates how to use the new generic methods added to the Container interface that mimic the `invoke` and `must invoke` functionality of the samber/do library without being tied to it.

## Overview

The `bootstrap.Container` interface now provides access to two generic functions:

- `bootstrap.Invoke[T any](container Container) (T, error)` - Retrieves a dependency and returns an error if it fails
- `bootstrap.MustInvoke[T any](container Container) T` - Retrieves a dependency and panics if it fails

These functions provide the same functionality as `do.Invoke` and `do.MustInvoke` but work through the Container interface, making your code less coupled to the samber/do library.

## Usage Examples

### Basic Usage

```go
package main

import (
    "context"
    "log"
    
    "demo/core/bootstrap"
    "github.com/samber/do"
    "github.com/uptrace/bun"
)

func ExampleUsage(container bootstrap.Container) {
    // Using Invoke (returns error)
    db, err := bootstrap.Invoke[*bun.DB](container)
    if err != nil {
        log.Printf("Failed to get database: %v", err)
        return
    }
    
    // Using MustInvoke (panics on error)
    db2 := bootstrap.MustInvoke[*bun.DB](container)
    
    // Both db and db2 are the same instance
    _ = db
    _ = db2
}
```

### Service Provider Example

```go
package provider

import (
    "context"
    "demo/core/bootstrap"
)

type MyServiceProvider struct{}

func (s *MyServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
    // Instead of using do.Invoke directly:
    // db, err := do.Invoke[*bun.DB](container.Injector())
    
    // Use the new generic function:
    db, err := bootstrap.Invoke[*bun.DB](container)
    if err != nil {
        return err
    }
    
    // Test database connection
    if err := db.Ping(); err != nil {
        return err
    }
    
    return nil
}
```

### Database Helper Functions

The `core/database` package now includes helper functions that use the Container interface:

```go
package main

import (
    "demo/core/bootstrap"
    "demo/core/database"
)

func ExampleDatabaseUsage(container bootstrap.Container) {
    // Using the new Container-based helper functions
    db, err := database.GetDBFromContainer(container)
    if err != nil {
        // Handle error
        return
    }
    
    // Or use the must version (panics on error)
    db2 := database.MustGetDBFromContainer(container)
    
    _ = db
    _ = db2
}
```

### Testing Example

```go
package mypackage

import (
    "testing"
    
    "demo/core/bootstrap"
    "github.com/samber/do"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
)

type TestService struct {
    Name string
}

func TestServiceUsage(t *testing.T) {
    // Setup
    injector := do.New()
    container := bootstrap.NewContainer(injector)
    
    // Register test service
    do.Provide(injector, func(i *do.Injector) (*TestService, error) {
        return &TestService{Name: "test"}, nil
    })
    
    // Test using Invoke
    service, err := bootstrap.Invoke[*TestService](container)
    require.NoError(t, err)
    assert.Equal(t, "test", service.Name)
    
    // Test using MustInvoke
    service2 := bootstrap.MustInvoke[*TestService](container)
    assert.Equal(t, "test", service2.Name)
}
```

## Benefits

1. **Decoupling**: Your code is less tied to the samber/do library implementation
2. **Consistency**: All dependency retrieval goes through the Container interface
3. **Testability**: Easier to mock the Container interface in tests
4. **Flexibility**: Can potentially switch DI libraries in the future without changing client code

## Migration Guide

### Before (directly using samber/do)

```go
db, err := do.Invoke[*bun.DB](container.Injector())
service := do.MustInvoke[*MyService](container.Injector())
```

### After (using Container generic functions)

```go
db, err := bootstrap.Invoke[*bun.DB](container)
service := bootstrap.MustInvoke[*MyService](container)
```

The functionality is identical, but the new approach provides better abstraction and decoupling.
