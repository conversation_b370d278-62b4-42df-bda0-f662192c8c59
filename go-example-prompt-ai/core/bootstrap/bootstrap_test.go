package bootstrap

import (
	"testing"

	"github.com/samber/do"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestService is a simple service for testing
type TestService struct {
	Name string
}

func NewTestService() *TestService {
	return &TestService{Name: "test-service"}
}

func TestInvoke(t *testing.T) {
	// Create a new injector and container
	injector := do.New()
	container := NewContainer(injector)

	// Register a test service
	do.Provide(injector, func(i *do.Injector) (*TestService, error) {
		return NewTestService(), nil
	})

	// Test Invoke function
	service, err := Invoke[*TestService](container)
	require.NoError(t, err)
	assert.NotNil(t, service)
	assert.Equal(t, "test-service", service.Name)
}

func TestMustInvoke(t *testing.T) {
	// Create a new injector and container
	injector := do.New()
	container := NewContainer(injector)

	// Register a test service
	do.Provide(injector, func(i *do.Injector) (*TestService, error) {
		return NewTestService(), nil
	})

	// Test MustInvoke function
	service := MustInvoke[*TestService](container)
	assert.NotNil(t, service)
	assert.Equal(t, "test-service", service.Name)
}

func TestMustInvokePanic(t *testing.T) {
	// Create a new injector and container without registering the service
	injector := do.New()
	container := NewContainer(injector)

	// Test that MustInvoke panics when service is not found
	assert.Panics(t, func() {
		MustInvoke[*TestService](container)
	})
}

func TestInvokeError(t *testing.T) {
	// Create a new injector and container without registering the service
	injector := do.New()
	container := NewContainer(injector)

	// Test that Invoke returns an error when service is not found
	service, err := Invoke[*TestService](container)
	assert.Error(t, err)
	assert.Nil(t, service)
}
