package bootstrap

import (
	"context"
	"fmt"
	"log/slog"
	"sync"

	"github.com/samber/do"
)

type TeardownFunc func(ctx context.Context) error

type Injector interface{
	Invoke[T any]() (T, error)
	MustInvoke[T any]() T
}

type Provider interface {
	Register(ctx context.Context, container Injector) error
	Boot(ctx context.Context, container Injector) error
	Teardown(ctx context.Context, container Injector) error
}

type BootStrapper interface {
	Injector() Injector
	Register(ctx context.Context) error
	Boot(ctx context.Context) error
	Shutdown(ctx context.Context) error
	Providers() []Provider
}

var _ Injector = (*DefaulInjector)(nil)

type DefaulInjector struct {
	injector *do.Injector
}

func NewInjector(i *do.Injector) *DefaulInjector {
	return &DefaulInjector{
		injector: i,
	}
}

// Invoke retrieves a dependency of type T from the container
// Returns the dependency and an error if retrieval fails
// This mimics the behavior of do.Invoke but without being tied to the library
func (c *DefaulInjector) Invoke[T any]() (T, error) {
	return do.Invoke[T](container.Injector())
}

// MustInvoke retrieves a dependency of type T from the container
// Panics if retrieval fails
// This mimics the behavior of do.MustInvoke but without being tied to the library
func (c *DefaulInjector) MustInvoke[T any]() T {
	return do.MustInvoke[T](container.Injector())
}


var _ Provider = (*DefaultProvider)(nil)

type DefaultProvider struct{}

func (s *DefaultProvider) Register(ctx context.Context, container Injector) error {
	return nil
}

func (s *DefaultProvider) Boot(ctx context.Context, container Injector) error {
	return nil
}

func (s *DefaultProvider) Teardown(ctx context.Context, container Injector) error {
	return nil
}

var _ BootStrapper = (*DefaultBootStrapper)(nil)

type DefaultBootStrapper struct {
	container    *DefaulInjector
	services     []Provider
	booted       bool
	serviceMutex sync.Mutex
	registered   bool
	shutdown     bool
}

func NewDefaultBootStrapper(i *do.Injector, services ...Provider) *DefaultBootStrapper {
	return &DefaultBootStrapper{
		container:    NewInjector(i),
		services:     services,
		booted:       false,
		registered:   false,
		serviceMutex: sync.Mutex{},
	}
}

func (b *DefaultBootStrapper) Injector() Injector {
	return b.container
}

func (b *DefaultBootStrapper) Register(ctx context.Context) error {
	if b.registered {
		slog.Warn("bootstrapper is already registered")
		return nil
	}
	b.registered = true
	for _, service := range b.services {
		if err := service.Register(ctx, b.container); err != nil {
			return err
		}
	}
	return nil
}

func (b *DefaultBootStrapper) Boot(ctx context.Context) error {
	if b.booted {
		slog.Warn("bootstrapper is already booted")
		return nil
	}
	b.booted = true
	for _, service := range b.services {
		if err := service.Boot(ctx, b.container); err != nil {
			return err
		}
	}
	return nil
}

func (b *DefaultBootStrapper) Shutdown(ctx context.Context) error {
	var errs []error
	if b.shutdown {
		slog.Warn("bootstrapper already shut down")
		return nil
	}
	b.shutdown = true

	// Iterate through services in reverse order
	for i := len(b.services) - 1; i >= 0; i-- {
		if err := b.services[i].Teardown(ctx, b.container); err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		err := fmt.Errorf("multiple teardown errors: %v", errs)
		slog.Error("shutdown failed", slog.Any("errors", errs))
		return err
	}
	return nil
}

func (b *DefaultBootStrapper) Providers() []Provider {
	b.serviceMutex.Lock()
	defer b.serviceMutex.Unlock()
	return b.services
}
