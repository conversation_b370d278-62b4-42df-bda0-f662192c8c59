package database

import (
	"demo/core/bootstrap"

	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// GetDB is a helper function to retrieve the database instance from the DI container
func GetDB(injector *do.Injector) (*bun.DB, error) {
	return do.Invoke[*bun.DB](injector)
}

// MustGetDB is a helper function that panics if the database cannot be retrieved
func MustGetDB(injector *do.Injector) *bun.DB {
	db, err := GetDB(injector)
	if err != nil {
		panic("failed to get database instance: " + err.Error())
	}
	return db
}

// GetDBFromContainer is a helper function to retrieve the database instance from the Container
// This uses the new generic Invoke method and is not tied to the samber/do library
func GetDBFromContainer(container bootstrap.Container) (*bun.DB, error) {
	return bootstrap.Invoke[*bun.DB](container)
}

// MustGetDBFromContainer is a helper function that panics if the database cannot be retrieved from the Container
// This uses the new generic MustInvoke method and is not tied to the samber/do library
func MustGetDBFromContainer(container bootstrap.Container) *bun.DB {
	return bootstrap.MustInvoke[*bun.DB](container)
}
